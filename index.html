<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>دليل أسعار السوق</title>

  <!-- Cairo Font + Bootstrap + Bootstrap Icons -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet" />

  <style>
    :root{
      /* ألوان سباركل */
      --brand:#1388BA;
      --brand-deep:#003FBF;
      --accent:#FED15D;
      --bg:#0b0f13;
      --panel:#111722;
      --line:rgba(255,255,255,.08);
      --muted:#a9b4bf;
      --text:#e9eef3;
      --success:#10b981;
      --danger:#ef4444;
      --radius:18px;
    }

    /* ====== Base ====== */
    html,body{height:100%}
    body{
      font-family:"Cairo",system-ui,-apple-system,Segoe UI,Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif;
      color:var(--text); background:var(--bg);
      background-image:
        radial-gradient(1200px 600px at 120% -10%, rgba(0,63,191,.12) 0%,transparent 60%),
        radial-gradient(1000px 500px at -20% 110%, rgba(19,136,186,.10) 0%,transparent 60%);
    }
    .container-narrow{max-width:1150px}

    a{color:var(--accent)}
    .muted{color:var(--muted)!important}
    .border-line{border-color:var(--line)!important}

    /* ====== Navbar ====== */
    .navbar{
      background:linear-gradient(90deg,#0b0f13,#0f1720);
      border-bottom:1px solid var(--line);
      backdrop-filter:saturate(140%) blur(6px);
    }
    .brand-badge{
      background:rgba(19,136,186,.18);
      color:#cfefff;
      border:1px solid rgba(19,136,186,.35);
      padding:.35rem .6rem;
      border-radius:10px;
      font-weight:700;
      margin-left:.35rem;
    }
    .nav-link{color:#cdd7e1!important}
    .nav-link:hover{color:#fff!important}

    /* ====== Hero ====== */
    .hero{
      background:
        linear-gradient(180deg, rgba(255,255,255,0.02), transparent 70%),
        radial-gradient(800px 400px at 0% -20%, rgba(254,209,93,.09) 0%, transparent 60%);
      border:1px solid var(--line);
      border-radius:calc(var(--radius) + 6px);
      padding:2.2rem;
    }
    .hero h1{letter-spacing:.3px}

    /* ====== Cards ====== */
    .card{
      background:linear-gradient(180deg, rgba(255,255,255,.02), rgba(255,255,255,.01));
      border:1px solid var(--line);
      border-radius:var(--radius);
      box-shadow:0 8px 22px rgba(0,0,0,.25);
      overflow:hidden;
    }
    .shadow-hover{transition:transform .18s ease, box-shadow .18s ease, border-color .18s ease}
    .shadow-hover:hover{
      transform:translateY(-4px);
      box-shadow:0 14px 36px rgba(0,0,0,.35);
      border-color:rgba(255,255,255,.16);
    }

    /* ====== Chips (categories) ====== */
    .chip{
      border-radius:999px;
      padding:.45rem .95rem;
      background:#0e1520;
      color:#d8dee9;
      border:1px solid var(--line);
      cursor:pointer; user-select:none;
      transition:all .15s ease;
    }
    .chip:hover{border-color:rgba(19,136,186,.5)}
    .chip.active{background:rgba(19,136,186,.18); border-color:rgba(19,136,186,.6); color:#e6f6ff}

    /* ====== Buttons ====== */
    .btn-brand{background:var(--brand); border:0}
    .btn-brand:hover{background:var(--brand-deep)}
    .btn-ghost{background:#0f1623; border:1px solid var(--line); color:#cfe0ee}
    .btn-ghost:hover{border-color:rgba(255,255,255,.18); color:#fff}

    /* ====== Product & Offer visuals ====== */
    .thumb{
      width:100%; height:150px; object-fit:cover; border-radius:12px;
      background:#0f1623
    }
    .price{font-weight:800; letter-spacing:.2px; color:var(--accent)}
    .badge-city{background:rgba(19,136,186,.16); color:#bfe8fb; border:1px solid rgba(19,136,186,.35)}

    /* ====== Skeleton Loader ====== */
    .skeleton{position:relative; overflow:hidden; background:#0f1623; border-radius:12px}
    .skeleton::after{
      content:""; position:absolute; inset:0;
      background:linear-gradient(90deg, transparent, rgba(255,255,255,.06), transparent);
      transform:translateX(-100%); animation:sk 1.25s infinite;
    }
    @keyframes sk{to{transform:translateX(100%)}}

    /* ====== Footer ====== */
    footer{border-top:1px solid var(--line)}
  </style>
</head>
<body>

  <!-- NAVBAR -->
  <nav class="navbar navbar-expand-lg sticky-top">
    <div class="container container-narrow">
      <a class="navbar-brand fw-bold text-white" href="#" onclick="showSection('home')">
        <span class="brand-badge">دليل</span> أسعار السوق
      </a>
      <button class="navbar-toggler text-white" type="button" data-bs-toggle="collapse" data-bs-target="#nav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div id="nav" class="collapse navbar-collapse">
        <ul class="navbar-nav me-auto">
          <li class="nav-item"><a class="nav-link" href="#" onclick="showSection('home')"><i class="bi bi-house-door me-1"></i>الرئيسية</a></li>
          <li class="nav-item"><a class="nav-link" href="#" onclick="loadOffersPage()"><i class="bi bi-tags me-1"></i>عروض المتاجر</a></li>
        </ul>
        <div class="d-none d-lg-flex align-items-center gap-3">
          <span class="badge text-bg-dark border border-line">العملة: ₪ ILS</span>
          <a class="btn btn-ghost btn-sm" href="mailto:<EMAIL>"><i class="bi bi-envelope"></i> اتصل بنا</a>
        </div>
      </div>
    </div>
  </nav>

  <main class="container container-narrow py-4">

    <!-- HOME -->
    <section id="home" class="section active">
      <!-- HERO -->
      <div class="hero mb-4">
        <div class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center justify-content-between gap-3">
          <div>
            <h1 class="fw-bold mb-2">اختر مدينتك لمعرفة أحدث الأسعار</h1>
            <p class="muted mb-0"><i class="bi bi-cloud-arrow-down"></i>يتم تحديث البيانات بشكل متتالي ودوري</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-brand"><i class="bi bi-geo-alt"></i> المدن المتاحة</button>
            <button class="btn btn-ghost" onclick="loadOffersPage()"><i class="bi bi-stars"></i> أحدث العروض</button>
          </div>
        </div>
      </div>

      <!-- Cities -->
      <div class="d-flex align-items-center justify-content-between mb-3">
        <h5 class="mb-0">المدن المتاحة</h5>
        <span class="muted small">اضغط على المدينة لعرض الأسعار</span>
      </div>
      <div id="citiesGrid" class="row g-3">
        <!-- Skeletons -->
        <div class="col-6 col-md-3"><div class="card p-4 shadow-hover skeleton" style="height:96px"></div></div>
        <div class="col-6 col-md-3"><div class="card p-4 shadow-hover skeleton" style="height:96px"></div></div>
        <div class="col-6 col-md-3"><div class="card p-4 shadow-hover skeleton" style="height:96px"></div></div>
        <div class="col-6 col-md-3"><div class="card p-4 shadow-hover skeleton" style="height:96px"></div></div>
      </div>

      <hr class="my-5 border-line">

      <!-- Latest Offers -->
      <div class="d-flex align-items-center justify-content-between mb-3">
        <h5 class="mb-0"><i class="bi bi-fire me-1"></i> أحدث العروض</h5>
        <a class="text-decoration-none" href="#" onclick="loadOffersPage()">عرض الكل <i class="bi bi-arrow-left-short"></i></a>
      </div>
      <div id="latestOffers" class="row g-3">
        <!-- Skeletons -->
        <div class="col-12 col-md-6 col-lg-3"><div class="card p-3 shadow-hover skeleton" style="height:160px"></div></div>
        <div class="col-12 col-md-6 col-lg-3"><div class="card p-3 shadow-hover skeleton" style="height:160px"></div></div>
        <div class="col-12 col-md-6 col-lg-3"><div class="card p-3 shadow-hover skeleton" style="height:160px"></div></div>
        <div class="col-12 col-md-6 col-lg-3"><div class="card p-3 shadow-hover skeleton" style="height:160px"></div></div>
      </div>
    </section>

    <!-- CITY / PRODUCTS -->
    <section id="city" class="section">
      <div class="d-flex align-items-center justify-content-between">
        <div>
          <h2 class="fw-bold mb-1" id="cityTitle">أسعار السوق</h2>
          <div class="muted small"><span class="badge badge-city">₪ ILS</span> الأسعار تتجدد دوريًا</div>
        </div>
        <button class="btn btn-ghost" onclick="showSection('home')"><i class="bi bi-arrow-right"></i> رجوع</button>
      </div>

      <div class="mt-3 d-flex flex-wrap gap-2" id="categoriesBar">
        <!-- chips injected -->
      </div>

      <div class="mt-3 row g-3" id="productsGrid">
        <!-- skeletons -->
        <div class="col-12 col-sm-6 col-lg-3"><div class="card p-3 shadow-hover skeleton" style="height:240px"></div></div>
        <div class="col-12 col-sm-6 col-lg-3"><div class="card p-3 shadow-hover skeleton" style="height:240px"></div></div>
        <div class="col-12 col-sm-6 col-lg-3"><div class="card p-3 shadow-hover skeleton" style="height:240px"></div></div>
        <div class="col-12 col-sm-6 col-lg-3"><div class="card p-3 shadow-hover skeleton" style="height:240px"></div></div>
      </div>
    </section>

    <!-- OFFERS -->
    <section id="offers" class="section">
      <div class="d-flex align-items-center justify-content-between">
        <h2 class="fw-bold"><i class="bi bi-tags"></i> عروض المتاجر</h2>
        <button class="btn btn-ghost" onclick="showSection('home')"><i class="bi bi-arrow-right"></i> رجوع</button>
      </div>

      <div class="mt-3 row g-3" id="offersGrid">
        <!-- skeletons -->
        <div class="col-12 col-md-6 col-lg-4"><div class="card p-3 shadow-hover skeleton" style="height:210px"></div></div>
        <div class="col-12 col-md-6 col-lg-4"><div class="card p-3 shadow-hover skeleton" style="height:210px"></div></div>
        <div class="col-12 col-md-6 col-lg-4"><div class="card p-3 shadow-hover skeleton" style="height:210px"></div></div>
      </div>
    </section>

  </main>

  <footer class="py-4">
    <div class="container container-narrow d-flex flex-wrap justify-content-between align-items-center gap-2">
      <div class="muted">© <span id="year"></span> دليل أسعار السوق</div>
      <div class="d-flex gap-3">
        <a class="text-decoration-none" href="#" onclick="alert('من نحن: منصة مستقلة لعرض الأسعار والعروض.');">من نحن</a>
        <a class="text-decoration-none" href="mailto:<EMAIL>">اتصل بنا</a>
      </div>
    </div>
  </footer>

  <!-- ====== JS (بدون تعديل على الدوال الوظيفية) ====== -->
  <script>
    function showSection(id){
      document.querySelectorAll('.section').forEach(s=>s.classList.remove('active'));
      document.getElementById(id).classList.add('active');
      window.scrollTo({top:0, behavior:'smooth'});
    }

    // تحميل المدن
    function loadCities(){
      const grid=document.getElementById('citiesGrid');
      grid.innerHTML='';
      google.script.run.withSuccessHandler(cities=>{
        grid.innerHTML='';
        if(!cities.length){
          grid.innerHTML=`<div class="col-12 text-center muted">لا توجد مدن متاحة</div>`;
          return;
        }
        cities.forEach(city=>{
          const col=document.createElement('div');
          col.className='col-6 col-md-3';
          col.innerHTML=`
            <div class="card p-3 shadow-hover h-100 city-card text-center" onclick="loadCity('${city}')">
              <div class="d-flex align-items-center justify-content-center gap-2">
                <i class="bi bi-geo-alt fs-4 text-info"></i>
                <h5 class="mb-0">${city}</h5>
              </div>
              <div class="muted small mt-2">اضغط للانتقال</div>
            </div>`;
          grid.appendChild(col);
        });
      }).listCities();
    }

    // أحدث العروض
    function loadLatestOffers(){
      const grid=document.getElementById('latestOffers');
      grid.innerHTML='';
      google.script.run.withSuccessHandler(offers=>{
        grid.innerHTML='';
        if(!offers.length){
          grid.innerHTML=`<div class="col-12 text-center muted">لا توجد عروض حالياً</div>`;
          return;
        }
        offers.forEach(o=>{
          const col=document.createElement('div');
          col.className='col-12 col-md-6 col-lg-3';
          col.innerHTML=`
            <div class="card p-3 shadow-hover h-100 offer-card">
              <div class="d-flex align-items-center gap-2 mb-2">
                ${o.StoreLogoURL ? `<img src="${o.StoreLogoURL}" alt="${o.StoreName}" style="width:40px;height:40px;object-fit:cover;border-radius:10px;border:1px solid var(--line)">` : `<div class="skeleton" style="width:40px;height:40px;border-radius:10px"></div>`}
                <div>
                  <div class="fw-semibold">${o.StoreName||'متجر'}</div>
                  <div class="muted small">${o.ExpiryDate ? ('ينتهي: '+o.ExpiryDate) : '—'}</div>
                </div>
              </div>
              <div class="fw-bold">${o.OfferTitle||''}</div>
              <div class="muted small mt-1">${o.OfferDescription||''}</div>
            </div>`;
          grid.appendChild(col);
        });
      }).getOffers({limit:4, activeOnly:true});
    }

    // صفحة مدينة
    function loadCity(city){
      showSection('city');
      document.getElementById('cityTitle').textContent=`أسعار السوق في ${city}`;
      loadCategories(city);
      loadProducts(city,'');
    }

    function loadCategories(city){
      const bar=document.getElementById('categoriesBar');
      bar.innerHTML='';
      google.script.run.withSuccessHandler(categories=>{
        const all=document.createElement('div');
        all.className='chip active'; all.textContent='الكل';
        all.onclick=()=>{ setActiveCategory(all); loadProducts(city,''); };
        bar.appendChild(all);
        categories.forEach(cat=>{
          const chip=document.createElement('div');
          chip.className='chip'; chip.textContent=cat;
          chip.onclick=()=>{ setActiveCategory(chip); loadProducts(city,cat); };
          bar.appendChild(chip);
        });
      }).getCategoriesByCity(city);
    }

    function setActiveCategory(chip){
      document.querySelectorAll('#categoriesBar .chip').forEach(c=>c.classList.remove('active'));
      chip.classList.add('active');
    }

    function loadProducts(city,category){
      const grid=document.getElementById('productsGrid');
      grid.innerHTML='';
      google.script.run.withSuccessHandler(products=>{
        grid.innerHTML='';
        if(!products.length){
          grid.innerHTML=`<div class="col-12 text-center muted">لا توجد منتجات</div>`;
          return;
        }
        products.forEach(p=>{
          const col=document.createElement('div');
          col.className='col-12 col-sm-6 col-lg-3';
          col.innerHTML=`
            <div class="card p-3 shadow-hover h-100 product-card">
              ${p.ImageURL ? `<img class="thumb" src="${p.ImageURL}" alt="${p.ProductName}">` : `<div class="thumb skeleton"></div>`}
              <div class="mt-3">
                <div class="d-flex align-items-start justify-content-between gap-2">
                  <h6 class="mb-0">${p.ProductName}</h6>
                  <span class="badge badge-city">${p.City||''}</span>
                </div>
                <div class="muted small mt-1">${p.Category||''}</div>
                <div class="d-flex align-items-center justify-content-between mt-3">
                  <div class="price">₪ ${Number(p.Price||0).toFixed(2)}</div>
                  <div class="muted small"><i class="bi bi-clock-history"></i> ${p.LastUpdated||'—'}</div>
                </div>
              </div>
            </div>`;
          grid.appendChild(col);
        });
      }).getProducts({city:city, category:category});
    }

    // صفحة العروض
    function loadOffersPage(){
      showSection('offers');
      const grid=document.getElementById('offersGrid');
      grid.innerHTML='';
      google.script.run.withSuccessHandler(offers=>{
        grid.innerHTML='';
        if(!offers.length){
          grid.innerHTML=`<div class="col-12 text-center muted">لا توجد عروض حالياً</div>`;
          return;
        }
        offers.forEach(o=>{
          const col=document.createElement('div');
          col.className='col-12 col-md-6 col-lg-4';
          col.innerHTML=`
            <div class="card p-3 shadow-hover h-100 offer-card">
              <div class="d-flex align-items-center gap-3 mb-2">
                ${o.StoreLogoURL ? `<img src="${o.StoreLogoURL}" alt="${o.StoreName}" style="width:56px;height:56px;object-fit:cover;border-radius:12px;border:1px solid var(--line)">` : `<div class="skeleton" style="width:56px;height:56px;border-radius:12px"></div>`}
                <div>
                  <div class="fw-semibold">${o.StoreName||'متجر'}</div>
                  <div class="muted small">${o.ExpiryDate ? ('ينتهي: '+o.ExpiryDate) : '—'}</div>
                </div>
              </div>
              <div class="fw-bold">${o.OfferTitle||''}</div>
              <div class="muted small mt-2">${o.OfferDescription||''}</div>
            </div>`;
          grid.appendChild(col);
        });
      }).getOffers({activeOnly:true});
    }

    document.addEventListener('DOMContentLoaded',()=>{
      document.getElementById('year').textContent=new Date().getFullYear();
      loadCities();
      loadLatestOffers();
    });
  </script>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
