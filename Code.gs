/** =========================================================
 *  Market Price Guide — Server (Apps Script)  [Code.gs]
 *  يقرأ البيانات مباشرة من Google Sheets ويقدّم الواجهة HTML
 *  للاستخدام مع google.script.run من واجهة index.html
 *  ========================================================= */

/** ضع معرّف الشيت هنا إن كان في ملف منفصل. اتركه فارغًا إذا كان المشروع مرتبطًا بنفس الشيت. */
const SPREADSHEET_ID = ''; // مثال: '1AbCDeFGhIJkLmNoPqrStUvWxYz1234567890'
const SHEET_PRODUCTS = 'Products';
const SHEET_OFFERS   = 'Stores_Offers';

/** ====== Helpers ====== */

/** فتح ورقة العمل */
function _openSheet(name) {
  const ss = SPREADSHEET_ID
    ? SpreadsheetApp.openById(SPREADSHEET_ID)
    : SpreadsheetApp.getActive();
  const sh = ss.getSheetByName(name);
  if (!sh) throw new Error(`لم يتم العثور على الشيت: ${name}`);
  return sh;
}

/** تحويل صفوف الشيت إلى مصفوفة كائنات حسب عناوين الأعمدة */
function _getValuesAsObjects_(sheet) {
  const values = sheet.getDataRange().getValues();
  if (values.length < 2) return [];
  const headers = values[0].map(h => String(h).trim());
  return values.slice(1).map(row => {
    const obj = {};
    headers.forEach((h, i) => obj[h] = row[i]);
    return obj;
  });
}

/** تنسيق التاريخ إلى yyyy-MM-dd */
function _toISODate(v) {
  if (!v) return '';
  try {
    if (v instanceof Date) {
      return Utilities.formatDate(v, Session.getScriptTimeZone(), 'yyyy-MM-dd');
    }
    const d = new Date(v);
    if (isNaN(d)) return String(v);
    return Utilities.formatDate(d, Session.getScriptTimeZone(), 'yyyy-MM-dd');
  } catch (e) {
    return '';
  }
}

/** تحويل إلى Boolean مرن */
function _toBool(v) {
  if (typeof v === 'boolean') return v;
  const s = String(v).trim().toLowerCase();
  return s === 'true' || s === '1' || s === 'yes';
}

/** تنظيف رقم السعر */
function _toNumber(n) {
  if (typeof n === 'number') return n;
  const v = Number(String(n).replace(/[^\d.\-]/g, ''));
  return isNaN(v) ? 0 : v;
}

/** ====== Public Functions (يتم استدعاؤها من الواجهة عبر google.script.run) ====== */

/**
 * قائمة المدن المتاحة من شيت Products (فريدة + مرتبة)
 * @return {string[]}
 */
function listCities() {
  const data = _getValuesAsObjects_(_openSheet(SHEET_PRODUCTS));
  const cities = data
    .map(r => String(r.City || '').trim())
    .filter(Boolean);
  return Array.from(new Set(cities)).sort();
}

/**
 * قائمة الفئات ضمن مدينة معينة
 * @param {string} city
 * @return {string[]}
 */
function getCategoriesByCity(city) {
  const data = _getValuesAsObjects_(_openSheet(SHEET_PRODUCTS));
  const categories = data
    .filter(r => String(r.City || '').trim() === String(city || '').trim())
    .map(r => String(r.Category || '').trim())
    .filter(Boolean);
  return Array.from(new Set(categories)).sort();
}

/**
 * جلب المنتجات (فلترة بالمدينة/الفئة/نص بحث)
 * @param {{city?:string, category?:string, q?:string}} params
 * @return {Object[]}
 */
function getProducts(params) {
  const { city = '', category = '', q = '' } = params || {};
  const data = _getValuesAsObjects_(_openSheet(SHEET_PRODUCTS));

  let rows = data;
  if (city) {
    const c = String(city).trim();
    rows = rows.filter(r => String(r.City || '').trim() === c);
  }
  if (category) {
    const cat = String(category).trim();
    rows = rows.filter(r => String(r.Category || '').trim() === cat);
  }
  if (q) {
    const needle = String(q).toLowerCase().trim();
    rows = rows.filter(r =>
      String(r.ProductName || '').toLowerCase().includes(needle) ||
      String(r.Category || '').toLowerCase().includes(needle) ||
      String(r.City || '').toLowerCase().includes(needle)
    );
  }

  return rows.map(r => ({
    ProductID:   String(r.ProductID || '').trim(),
    ProductName: String(r.ProductName || '').trim(),
    Category:    String(r.Category || '').trim(),
    City:        String(r.City || '').trim(),
    Price:       _toNumber(r.Price),
    ImageURL:    String(r.ImageURL || '').trim(),
    LastUpdated: _toISODate(r.LastUpdated)
  }));
}

/**
 * جلب عروض المتاجر
 * @param {{limit?:number, activeOnly?:boolean}} params
 * @return {Object[]}
 */
function getOffers(params) {
  const { limit = 0, activeOnly = true } = params || {};
  const data = _getValuesAsObjects_(_openSheet(SHEET_OFFERS));

  let rows = data;
  if (activeOnly) rows = rows.filter(r => _toBool(r.IsActive));

  // ترتيب حسب أقرب انتهاء (الأقرب أولًا). إن لم يوجد تاريخ، يذهب للنهاية.
  rows.sort((a, b) => {
    const ad = a.ExpiryDate ? new Date(a.ExpiryDate) : new Date('9999-12-31');
    const bd = b.ExpiryDate ? new Date(b.ExpiryDate) : new Date('9999-12-31');
    return ad - bd;
  });

  const sliced = limit > 0 ? rows.slice(0, limit) : rows;

  return sliced.map(r => ({
    OfferID:         String(r.OfferID || '').trim(),
    StoreName:       String(r.StoreName || '').trim(),
    StoreLogoURL:    String(r.StoreLogoURL || '').trim(),
    OfferTitle:      String(r.OfferTitle || '').trim(),
    OfferDescription:String(r.OfferDescription || '').trim(),
    ExpiryDate:      _toISODate(r.ExpiryDate),
    IsActive:        _toBool(r.IsActive)
  }));
}

/** ====== HTTP Entry (يقدّم الواجهة فقط — لا JSON) ====== */

/**
 * تقديم واجهة HTML (index.html داخل مشروع Apps Script)
 * ملاحظة: الواجهة ستستخدم google.script.run لاستدعاء الدوال أعلاه.
 */
function doGet() {
  return HtmlService
    .createHtmlOutputFromFile('index') // تأكد أن اسم ملف الواجهة هو index.html داخل المشروع
    .setTitle('دليل أسعار السوق')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL); // للسماح بالـ iframe عند الحاجة
}

/** ====== (اختياري) تضمين أجزاء HTML أخرى عبر <?!= include('FileName') ?> ====== */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}
